#!/bin/bash

# 电影评分分析 MapReduce 作业运行脚本
# 作者：MapReduce高阶编程作业
# 功能：编译、打包、上传数据、运行MapReduce作业

echo "=== 电影评分分析 MapReduce 作业 ==="
echo "开始时间: $(date)"
echo

# 设置变量
PROJECT_NAME="movie-rating-analysis"
JAR_NAME="${PROJECT_NAME}-1.0-SNAPSHOT-jar-with-dependencies.jar"
MAIN_CLASS="com.hadoop.movieanalysis.MovieRatingAnalysis"
HDFS_INPUT_DIR="/user/$(whoami)/movie-analysis/input"
HDFS_OUTPUT_DIR="/user/$(whoami)/movie-analysis/output"
LOCAL_DATA_DIR="."

# 检查Hadoop是否运行
echo "1. 检查Hadoop服务状态..."
if ! jps | grep -q "NameNode\|DataNode"; then
    echo "错误: Hadoop服务未运行，请先启动Hadoop集群"
    echo "请运行: start-dfs.sh 和 start-yarn.sh"
    exit 1
fi
echo "✓ Hadoop服务正在运行"
echo

# 编译项目
echo "2. 编译Maven项目..."
if mvn clean compile; then
    echo "✓ 编译成功"
else
    echo "✗ 编译失败"
    exit 1
fi
echo

# 打包项目
echo "3. 打包项目..."
if mvn package; then
    echo "✓ 打包成功"
else
    echo "✗ 打包失败"
    exit 1
fi
echo

# 检查JAR文件是否存在
if [ ! -f "target/${JAR_NAME}" ]; then
    echo "✗ JAR文件不存在: target/${JAR_NAME}"
    exit 1
fi
echo "✓ JAR文件已生成: target/${JAR_NAME}"
echo

# 创建HDFS目录
echo "4. 准备HDFS目录..."
hdfs dfs -rm -r ${HDFS_OUTPUT_DIR} 2>/dev/null || true
hdfs dfs -mkdir -p ${HDFS_INPUT_DIR}
hdfs dfs -mkdir -p $(dirname ${HDFS_OUTPUT_DIR})
echo "✓ HDFS目录准备完成"
echo

# 上传数据文件到HDFS
echo "5. 上传数据文件到HDFS..."
if [ -f "${LOCAL_DATA_DIR}/ratings.dat" ]; then
    hdfs dfs -put -f ${LOCAL_DATA_DIR}/ratings.dat ${HDFS_INPUT_DIR}/
    echo "✓ ratings.dat 上传成功"
else
    echo "✗ 找不到 ratings.dat 文件"
    exit 1
fi

# 可选：上传其他数据文件
if [ -f "${LOCAL_DATA_DIR}/movies.dat" ]; then
    hdfs dfs -put -f ${LOCAL_DATA_DIR}/movies.dat ${HDFS_INPUT_DIR}/
    echo "✓ movies.dat 上传成功"
fi

if [ -f "${LOCAL_DATA_DIR}/users.dat" ]; then
    hdfs dfs -put -f ${LOCAL_DATA_DIR}/users.dat ${HDFS_INPUT_DIR}/
    echo "✓ users.dat 上传成功"
fi
echo

# 显示HDFS输入目录内容
echo "6. 检查HDFS输入目录..."
hdfs dfs -ls ${HDFS_INPUT_DIR}
echo

# 运行MapReduce作业
echo "7. 运行MapReduce作业..."
echo "输入路径: ${HDFS_INPUT_DIR}/ratings.dat"
echo "输出路径: ${HDFS_OUTPUT_DIR}"
echo

if hadoop jar target/${JAR_NAME} ${MAIN_CLASS} ${HDFS_INPUT_DIR}/ratings.dat ${HDFS_OUTPUT_DIR}; then
    echo "✓ MapReduce作业执行成功"
else
    echo "✗ MapReduce作业执行失败"
    exit 1
fi
echo

# 显示结果
echo "8. 显示分析结果..."
echo "--- 输出目录内容 ---"
hdfs dfs -ls ${HDFS_OUTPUT_DIR}
echo

echo "--- 分析结果（前20行）---"
hdfs dfs -cat ${HDFS_OUTPUT_DIR}/part-r-00000 | head -20
echo

echo "--- 结果统计 ---"
echo "总输出行数: $(hdfs dfs -cat ${HDFS_OUTPUT_DIR}/part-r-* | wc -l)"
echo

# 可选：下载结果到本地
echo "9. 下载结果到本地..."
rm -rf output_local 2>/dev/null || true
hdfs dfs -get ${HDFS_OUTPUT_DIR} output_local
echo "✓ 结果已下载到 output_local 目录"
echo

echo "=== 作业完成 ==="
echo "结束时间: $(date)"
echo "结果文件位置:"
echo "  HDFS: ${HDFS_OUTPUT_DIR}"
echo "  本地: output_local/"
echo
echo "查看完整结果命令:"
echo "  hdfs dfs -cat ${HDFS_OUTPUT_DIR}/part-r-*"
echo "  或者查看本地文件: cat output_local/part-r-*"
