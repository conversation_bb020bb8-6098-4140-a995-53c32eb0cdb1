# 电影评分分析 - MapReduce高阶编程

## 项目概述

本项目是一个基于Hadoop MapReduce的电影网站用户影评分析系统，实现了MapReduce高阶编程技术，包括Combiner和Partitioner策略优化。

### 主要功能

1. **电影评分统计分析**：计算每部电影的平均评分、最高评分、最低评分和评分次数
2. **性能优化**：使用Combiner减少网络传输，使用自定义Partitioner提高并行度
3. **数据质量分析**：统计高评分、中等评分、低评分电影的数量
4. **热门电影识别**：识别评分次数超过100次的热门电影

### 技术特点

- ✅ **Combiner优化**：在Map阶段本地聚合数据，减少网络传输
- ✅ **自定义Partitioner**：根据电影ID范围均匀分配数据到不同Reducer
- ✅ **自定义数据类型**：实现MovieRatingWritable优化数据序列化
- ✅ **错误处理**：完善的异常处理和数据验证
- ✅ **统计计数器**：详细的作业执行统计信息

## 环境要求

- **Java版本**：JDK 8 或更高版本
- **Hadoop版本**：3.1.3
- **Maven版本**：3.6 或更高版本
- **操作系统**：Windows/Linux/macOS

## 项目结构

```
ldl/
├── src/main/java/com/hadoop/movieanalysis/
│   ├── MovieRatingAnalysis.java      # 主作业类
│   ├── MovieStatsMapper.java         # Mapper类
│   ├── MovieStatsReducer.java        # Reducer类
│   ├── MovieStatsCombiner.java       # Combiner类
│   ├── MoviePartitioner.java         # 自定义Partitioner
│   └── MovieRatingWritable.java      # 自定义数据类型
├── pom.xml                           # Maven配置文件
├── run.bat                           # Windows运行脚本
├── run.sh                            # Linux/macOS运行脚本
├── ratings.dat                       # 评分数据
├── movies.dat                        # 电影数据
├── users.dat                         # 用户数据
└── README.md                         # 项目说明
```

## 数据格式

### ratings.dat
```
UserID::MovieID::Rating::Timestamp
1::1193::5::978300760
1::661::3::978302109
```

### movies.dat
```
MovieID::Title::Genres
1::Toy Story (1995)::Animation|Children's|Comedy
2::Jumanji (1995)::Adventure|Children's|Fantasy
```

### users.dat
```
UserID::Gender::Age::Occupation::Zip-code
1::F::1::10::48067
2::M::56::16::70072
```

## 快速开始

### 1. 确保Hadoop集群运行

**Windows:**
```cmd
start-dfs.cmd
start-yarn.cmd
jps
```

**Linux/macOS:**
```bash
start-dfs.sh
start-yarn.sh
jps
```

### 2. 运行项目

**Windows:**
```cmd
run.bat
```

**Linux/macOS:**
```bash
./run.sh
```

### 3. 手动运行（可选）

```bash
# 编译打包
mvn clean package

# 创建HDFS目录
hdfs dfs -mkdir -p /user/$USER/movie-analysis/input
hdfs dfs -rm -r /user/$USER/movie-analysis/output

# 上传数据
hdfs dfs -put ratings.dat /user/$USER/movie-analysis/input/

# 运行作业
hadoop jar target/movie-rating-analysis-1.0-SNAPSHOT-jar-with-dependencies.jar \
  com.hadoop.movieanalysis.MovieRatingAnalysis \
  /user/$USER/movie-analysis/input/ratings.dat \
  /user/$USER/movie-analysis/output

# 查看结果
hdfs dfs -cat /user/$USER/movie-analysis/output/part-r-*
```

## 输出结果格式

```
电影ID    平均评分:X.XX    评分次数:XXX    最高评分:X.X    最低评分:X.X    总评分:XXX.X
1         平均评分:4.15    评分次数:452    最高评分:5.0    最低评分:1.0    总评分:1875.0
2         平均评分:3.20    评分次数:131    最高评分:5.0    最低评分:1.0    总评分:419.0
```

## 性能优化说明

### 1. Combiner优化
- **作用**：在Map阶段本地聚合相同电影的评分数据
- **效果**：减少网络传输量，提高作业执行效率
- **实现**：MovieStatsCombiner类

### 2. Partitioner优化
- **作用**：根据电影ID范围将数据均匀分配到不同Reducer
- **效果**：提高并行处理效率，避免数据倾斜
- **实现**：MoviePartitioner类

### 3. 自定义数据类型
- **作用**：优化数据序列化和传输
- **效果**：减少内存使用，提高处理速度
- **实现**：MovieRatingWritable类

## 作业统计信息

运行完成后会显示以下统计信息：

- **处理的记录数**：总共处理的评分记录数量
- **处理的电影数**：分析的电影总数
- **总评分数**：所有评分的总和
- **高评分电影数**：平均评分>=4.0的电影数量
- **中等评分电影数**：平均评分3.0-4.0的电影数量
- **低评分电影数**：平均评分<3.0的电影数量
- **热门电影数**：评分次数>=100的电影数量

## 故障排除

### 1. 编译错误
```bash
# 检查Java版本
java -version
javac -version

# 检查Maven版本
mvn -version
```

### 2. Hadoop连接错误
```bash
# 检查Hadoop服务
jps
hdfs dfsadmin -report
```

### 3. 内存不足
```bash
# 增加JVM内存
export HADOOP_HEAPSIZE=2048
```

### 4. 权限问题
```bash
# 检查HDFS权限
hdfs dfs -ls /user/$USER/
```

## 扩展功能

可以基于此项目实现更多分析功能：

1. **电影类型分析**：分析不同类型电影的评分分布
2. **用户行为分析**：分析不同年龄段用户的评分偏好
3. **时间趋势分析**：分析评分随时间的变化趋势
4. **推荐系统**：基于评分数据实现电影推荐

## 作者信息

- **课程**：MapReduce高阶编程
- **作业**：电影网站用户影评分析
- **技术栈**：Hadoop 3.1.3 + Java 8 + Maven
- **开发环境**：VSCode + Windows/Linux
