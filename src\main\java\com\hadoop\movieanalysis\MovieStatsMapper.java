package com.hadoop.movieanalysis;

import java.io.IOException;
import java.util.StringTokenizer;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

/**
 * Mapper类：处理ratings.dat文件
 * 输入格式：UserID::MovieID::Rating::Timestamp
 * 输出：Key=MovieID, Value=MovieRatingWritable(包含评分信息)
 */
public class MovieStatsMapper extends Mapper<LongWritable, Text, IntWritable, MovieRatingWritable> {
    
    private IntWritable movieId = new IntWritable();
    private MovieRatingWritable ratingInfo = new MovieRatingWritable();
    
    @Override
    public void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        // 解析输入行：UserID::MovieID::Rating::Timestamp
        String line = value.toString().trim();
        
        // 跳过空行
        if (line.isEmpty()) {
            return;
        }
        
        try {
            // 使用::分割字段
            String[] fields = line.split("::");
            
            // 检查字段数量是否正确
            if (fields.length != 4) {
                // 记录错误但不中断处理
                context.getCounter("MAPPER_ERRORS", "INVALID_FIELD_COUNT").increment(1);
                return;
            }
            
            // 解析字段
            int userId = Integer.parseInt(fields[0].trim());
            int movieIdValue = Integer.parseInt(fields[1].trim());
            double rating = Double.parseDouble(fields[2].trim());
            long timestamp = Long.parseLong(fields[3].trim());
            
            // 验证评分范围（通常是1-5）
            if (rating < 1.0 || rating > 5.0) {
                context.getCounter("MAPPER_ERRORS", "INVALID_RATING_RANGE").increment(1);
                return;
            }
            
            // 设置输出的key和value
            movieId.set(movieIdValue);
            ratingInfo = new MovieRatingWritable(rating);
            
            // 输出到context
            context.write(movieId, ratingInfo);
            
            // 统计处理的记录数
            context.getCounter("MAPPER_STATS", "PROCESSED_RECORDS").increment(1);
            
        } catch (NumberFormatException e) {
            // 处理数字格式错误
            context.getCounter("MAPPER_ERRORS", "NUMBER_FORMAT_ERROR").increment(1);
            System.err.println("数字格式错误，行内容: " + line);
        } catch (Exception e) {
            // 处理其他异常
            context.getCounter("MAPPER_ERRORS", "OTHER_ERRORS").increment(1);
            System.err.println("处理行时发生错误: " + line + ", 错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        super.setup(context);
        // 可以在这里进行初始化工作
        System.out.println("MovieStatsMapper 初始化完成");
    }
    
    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        super.cleanup(context);
        // 可以在这里进行清理工作
        System.out.println("MovieStatsMapper 清理完成");
    }
}
