#!/bin/bash

# MapReduce电影评分分析 - 项目传输后部署脚本
# 根目录: /root/
# 使用javac编译，不使用Maven
# 适用于项目文件已传输到CentOS的情况

echo "=========================================="
echo "  MapReduce电影评分分析 - 项目部署脚本"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 设置变量
SOURCE_PROJECT_DIR="/root/ldl"  # 原项目目录（传输后的位置）
DEPLOY_DIR="/root/movie-analysis-deploy"  # 部署目录
HADOOP_HOME="/opt/hadoop"  # 根据实际安装路径调整

# 第一步：检查原项目文件
echo "第一步：检查原项目文件..."
if [ ! -d "$SOURCE_PROJECT_DIR" ]; then
    echo "✗ 原项目目录不存在: $SOURCE_PROJECT_DIR"
    echo "请确认项目文件已正确传输到CentOS系统"
    echo "如果项目目录名称不同，请修改脚本中的 SOURCE_PROJECT_DIR 变量"
    exit 1
fi

echo "✓ 原项目目录存在: $SOURCE_PROJECT_DIR"

# 检查关键文件
echo "检查关键文件..."
if [ -d "$SOURCE_PROJECT_DIR/src/main/java/com/hadoop/movieanalysis" ]; then
    echo "✓ Java源代码目录存在"
else
    echo "✗ Java源代码目录不存在"
    exit 1
fi

if [ -f "$SOURCE_PROJECT_DIR/ratings.dat" ]; then
    echo "✓ ratings.dat 数据文件存在"
else
    echo "✗ ratings.dat 数据文件不存在"
    exit 1
fi

# 第二步：创建部署目录结构
echo ""
echo "第二步：创建部署目录结构..."
mkdir -p $DEPLOY_DIR/src/com/hadoop/movieanalysis
mkdir -p $DEPLOY_DIR/classes
mkdir -p $DEPLOY_DIR/data
mkdir -p $DEPLOY_DIR/scripts
mkdir -p $DEPLOY_DIR/results
echo "✓ 部署目录结构创建完成"

# 第三步：复制项目文件
echo ""
echo "第三步：复制项目文件..."

# 复制Java源代码
echo "复制Java源代码文件..."
cp $SOURCE_PROJECT_DIR/src/main/java/com/hadoop/movieanalysis/*.java $DEPLOY_DIR/src/com/hadoop/movieanalysis/
if [ $? -eq 0 ]; then
    echo "✓ Java源代码文件复制完成"
    ls -la $DEPLOY_DIR/src/com/hadoop/movieanalysis/
else
    echo "✗ Java源代码文件复制失败"
    exit 1
fi

# 复制数据文件
echo ""
echo "复制数据文件..."
cp $SOURCE_PROJECT_DIR/*.dat $DEPLOY_DIR/data/ 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ 数据文件复制完成"
    ls -la $DEPLOY_DIR/data/
else
    echo "✗ 数据文件复制失败"
    exit 1
fi

# 第四步：设置环境变量
echo ""
echo "第四步：设置环境变量..."

# 自动检测Java路径
if [ -z "$JAVA_HOME" ]; then
    export JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
fi

# 检查Hadoop路径
if [ ! -d "$HADOOP_HOME" ]; then
    echo "警告: Hadoop目录不存在: $HADOOP_HOME"
    echo "请设置正确的HADOOP_HOME路径"
    echo "常见路径: /opt/hadoop, /usr/local/hadoop, /root/hadoop"

    # 尝试自动检测
    for path in /opt/hadoop /usr/local/hadoop /root/hadoop; do
        if [ -d "$path" ]; then
            export HADOOP_HOME="$path"
            echo "自动检测到Hadoop路径: $HADOOP_HOME"
            break
        fi
    done

    if [ ! -d "$HADOOP_HOME" ]; then
        echo "无法自动检测Hadoop路径，请手动设置"
        exit 1
    fi
fi

export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

echo "✓ 环境变量设置完成"
echo "JAVA_HOME: $JAVA_HOME"
echo "HADOOP_HOME: $HADOOP_HOME"

# 第五步：编译项目
echo ""
echo "第五步：编译项目..."

# 进入部署目录
cd $DEPLOY_DIR

# 编译Java源文件
echo "编译Java源文件..."
cd src/com/hadoop/movieanalysis
javac -cp $HADOOP_CLASSPATH -d $DEPLOY_DIR/classes *.java

if [ $? -eq 0 ]; then
    echo "✓ Java文件编译成功"
else
    echo "✗ Java文件编译失败"
    echo "请检查Hadoop环境和Java源代码"
    exit 1
fi

# 创建JAR文件
echo "创建JAR文件..."
cd $DEPLOY_DIR/classes
jar cf $DEPLOY_DIR/movie-analysis.jar com/hadoop/movieanalysis/*.class

if [ -f "$DEPLOY_DIR/movie-analysis.jar" ]; then
    echo "✓ JAR文件创建成功"
    ls -la $DEPLOY_DIR/movie-analysis.jar
else
    echo "✗ JAR文件创建失败"
    exit 1
fi

# 第六步：创建快速运行脚本
echo ""
echo "第六步：创建快速运行脚本..."

cat > $DEPLOY_DIR/quick_run.sh << 'EOF'
#!/bin/bash

echo "=== MapReduce电影评分分析 - 快速运行 ==="
echo "开始时间: $(date)"

# 设置变量
DEPLOY_DIR="/root/movie-analysis-deploy"
HADOOP_HOME="/opt/hadoop"  # 请根据实际路径调整

# 进入部署目录
cd $DEPLOY_DIR

# 设置环境变量
export JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin

# 检查Hadoop服务
echo "1. 检查Hadoop服务..."
if ! jps | grep -q "NameNode\|DataNode"; then
    echo "启动Hadoop服务..."
    $HADOOP_HOME/bin/hdfs namenode -format -force
    $HADOOP_HOME/sbin/start-dfs.sh
    $HADOOP_HOME/sbin/start-yarn.sh
    sleep 20
fi

# 准备HDFS数据
echo "2. 准备HDFS数据..."
$HADOOP_HOME/bin/hdfs dfs -mkdir -p /user/root/movie-analysis/input
$HADOOP_HOME/bin/hdfs dfs -rm -r /user/root/movie-analysis/output 2>/dev/null || true
$HADOOP_HOME/bin/hdfs dfs -put data/ratings.dat /user/root/movie-analysis/input/ 2>/dev/null || echo "数据文件可能已存在"

# 运行MapReduce作业
echo "3. 运行MapReduce作业..."
$HADOOP_HOME/bin/hadoop jar movie-analysis.jar com.hadoop.movieanalysis.MovieRatingAnalysis /user/root/movie-analysis/input/ratings.dat /user/root/movie-analysis/output

# 查看结果
echo "4. 查看结果..."
$HADOOP_HOME/bin/hdfs dfs -cat /user/root/movie-analysis/output/part-r-00000 | head -10

echo "结束时间: $(date)"
echo "=== 运行完成 ==="
EOF

chmod +x $DEPLOY_DIR/quick_run.sh

echo "✓ 快速运行脚本创建完成"

echo ""
echo "=========================================="
echo "  部署完成！"
echo "=========================================="
echo ""
echo "部署目录: $DEPLOY_DIR"
echo ""
echo "项目文件检查："
echo "✓ Java源代码: $(ls $DEPLOY_DIR/src/com/hadoop/movieanalysis/*.java | wc -l) 个文件"
echo "✓ 数据文件: $(ls $DEPLOY_DIR/data/*.dat | wc -l) 个文件"
echo "✓ JAR文件: $([ -f "$DEPLOY_DIR/movie-analysis.jar" ] && echo "存在" || echo "不存在")"
echo "✓ 快速运行脚本: $([ -f "$DEPLOY_DIR/quick_run.sh" ] && echo "存在" || echo "不存在")"
echo ""
echo "下一步操作："
echo "1. 运行快速脚本: cd $DEPLOY_DIR && ./quick_run.sh"
echo "2. 或者按照详细教程手动执行: 完整非本地部署教程.md"
echo ""
echo "快速运行命令："
echo "cd $DEPLOY_DIR"
echo "./quick_run.sh"
echo ""
echo "结束时间: $(date)"
