#!/bin/bash

# MapReduce电影评分分析 - 一键部署脚本
# 根目录: /root/
# 使用javac编译，不使用Maven

echo "=========================================="
echo "  MapReduce电影评分分析 - 一键部署脚本"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 设置变量
PROJECT_DIR="/root/movie-analysis"
HADOOP_HOME="/root/hadoop"  # 根据实际安装路径调整
JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"  # 根据实际Java路径调整

# 第一步：创建项目目录结构
echo "第一步：创建项目目录结构..."
mkdir -p $PROJECT_DIR/src/com/hadoop/movieanalysis
mkdir -p $PROJECT_DIR/classes
mkdir -p $PROJECT_DIR/data
mkdir -p $PROJECT_DIR/scripts
mkdir -p $PROJECT_DIR/results
echo "✓ 目录结构创建完成"

# 第二步：设置环境变量
echo "第二步：设置环境变量..."
export JAVA_HOME=$JAVA_HOME
export HADOOP_HOME=$HADOOP_HOME
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*
echo "✓ 环境变量设置完成"

# 第三步：创建Java源代码文件
echo "第三步：创建Java源代码文件..."

# 创建MovieRatingWritable.java
cat > $PROJECT_DIR/src/com/hadoop/movieanalysis/MovieRatingWritable.java << 'EOF'
package com.hadoop.movieanalysis;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import org.apache.hadoop.io.WritableComparable;

public class MovieRatingWritable implements WritableComparable<MovieRatingWritable> {
    
    private double totalRating;
    private int ratingCount;
    private double maxRating;
    private double minRating;
    
    public MovieRatingWritable() {
        this.totalRating = 0.0;
        this.ratingCount = 0;
        this.maxRating = 0.0;
        this.minRating = 5.0;
    }
    
    public MovieRatingWritable(double rating) {
        this.totalRating = rating;
        this.ratingCount = 1;
        this.maxRating = rating;
        this.minRating = rating;
    }
    
    public MovieRatingWritable(double totalRating, int ratingCount, 
                              double maxRating, double minRating) {
        this.totalRating = totalRating;
        this.ratingCount = ratingCount;
        this.maxRating = maxRating;
        this.minRating = minRating;
    }
    
    public void merge(MovieRatingWritable other) {
        this.totalRating += other.totalRating;
        this.ratingCount += other.ratingCount;
        this.maxRating = Math.max(this.maxRating, other.maxRating);
        this.minRating = Math.min(this.minRating, other.minRating);
    }
    
    public double getAverageRating() {
        return ratingCount > 0 ? totalRating / ratingCount : 0.0;
    }
    
    public double getTotalRating() { return totalRating; }
    public void setTotalRating(double totalRating) { this.totalRating = totalRating; }
    public int getRatingCount() { return ratingCount; }
    public void setRatingCount(int ratingCount) { this.ratingCount = ratingCount; }
    public double getMaxRating() { return maxRating; }
    public void setMaxRating(double maxRating) { this.maxRating = maxRating; }
    public double getMinRating() { return minRating; }
    public void setMinRating(double minRating) { this.minRating = minRating; }
    
    @Override
    public void write(DataOutput out) throws IOException {
        out.writeDouble(totalRating);
        out.writeInt(ratingCount);
        out.writeDouble(maxRating);
        out.writeDouble(minRating);
    }
    
    @Override
    public void readFields(DataInput in) throws IOException {
        totalRating = in.readDouble();
        ratingCount = in.readInt();
        maxRating = in.readDouble();
        minRating = in.readDouble();
    }
    
    @Override
    public int compareTo(MovieRatingWritable other) {
        double thisAvg = this.getAverageRating();
        double otherAvg = other.getAverageRating();
        return Double.compare(otherAvg, thisAvg);
    }
    
    @Override
    public String toString() {
        return String.format("平均评分:%.2f, 评分次数:%d, 最高评分:%.1f, 最低评分:%.1f",
                getAverageRating(), ratingCount, maxRating, minRating);
    }
}
EOF

# 创建MovieStatsMapper.java
cat > $PROJECT_DIR/src/com/hadoop/movieanalysis/MovieStatsMapper.java << 'EOF'
package com.hadoop.movieanalysis;

import java.io.IOException;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

public class MovieStatsMapper extends Mapper<LongWritable, Text, IntWritable, MovieRatingWritable> {
    
    private IntWritable movieId = new IntWritable();
    private MovieRatingWritable ratingInfo = new MovieRatingWritable();
    
    @Override
    public void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString().trim();
        
        if (line.isEmpty()) {
            return;
        }
        
        try {
            String[] fields = line.split("::");
            
            if (fields.length != 4) {
                context.getCounter("MAPPER_ERRORS", "INVALID_FIELD_COUNT").increment(1);
                return;
            }
            
            int movieIdValue = Integer.parseInt(fields[1].trim());
            double rating = Double.parseDouble(fields[2].trim());
            
            if (rating < 1.0 || rating > 5.0) {
                context.getCounter("MAPPER_ERRORS", "INVALID_RATING_RANGE").increment(1);
                return;
            }
            
            movieId.set(movieIdValue);
            ratingInfo = new MovieRatingWritable(rating);
            
            context.write(movieId, ratingInfo);
            context.getCounter("MAPPER_STATS", "PROCESSED_RECORDS").increment(1);
            
        } catch (NumberFormatException e) {
            context.getCounter("MAPPER_ERRORS", "NUMBER_FORMAT_ERROR").increment(1);
        } catch (Exception e) {
            context.getCounter("MAPPER_ERRORS", "OTHER_ERRORS").increment(1);
        }
    }
}
EOF

echo "✓ Java源代码文件创建完成"

# 第四步：检查数据文件
echo "第四步：检查数据文件..."
if [ -f "/root/ratings.dat" ]; then
    cp /root/ratings.dat $PROJECT_DIR/data/
    echo "✓ ratings.dat 文件复制完成"
else
    echo "⚠ 警告: 未找到 /root/ratings.dat 文件，请手动复制数据文件到 $PROJECT_DIR/data/"
fi

if [ -f "/root/movies.dat" ]; then
    cp /root/movies.dat $PROJECT_DIR/data/
    echo "✓ movies.dat 文件复制完成"
fi

if [ -f "/root/users.dat" ]; then
    cp /root/users.dat $PROJECT_DIR/data/
    echo "✓ users.dat 文件复制完成"
fi

echo ""
echo "=========================================="
echo "  部署完成！"
echo "=========================================="
echo ""
echo "项目目录: $PROJECT_DIR"
echo "下一步操作："
echo "1. 确保Hadoop已安装在 $HADOOP_HOME"
echo "2. 运行完整部署教程中的编译和运行步骤"
echo "3. 或者查看详细教程: 完整非本地部署教程.md"
echo ""
echo "快速运行命令："
echo "cd $PROJECT_DIR"
echo "# 编译项目"
echo "javac -cp \$HADOOP_CLASSPATH -d classes src/com/hadoop/movieanalysis/*.java"
echo "cd classes && jar cf ../movie-analysis.jar com/hadoop/movieanalysis/*.class && cd .."
echo "# 启动Hadoop并运行作业"
echo "\$HADOOP_HOME/sbin/start-dfs.sh"
echo "\$HADOOP_HOME/sbin/start-yarn.sh"
echo "\$HADOOP_HOME/bin/hadoop jar movie-analysis.jar com.hadoop.movieanalysis.MovieRatingAnalysis /input/ratings.dat /output"
echo ""
echo "结束时间: $(date)"
