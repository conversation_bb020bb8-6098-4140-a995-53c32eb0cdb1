package com.hadoop.movieanalysis;

import java.io.IOException;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.mapreduce.Reducer;

/**
 * Combiner类：在Map阶段本地聚合数据，减少网络传输
 * 输入：Key=MovieID, Value=Iterable<MovieRatingWritable>
 * 输出：Key=MovieID, Value=MovieRatingWritable(聚合后的评分信息)
 */
public class MovieStatsCombiner extends Reducer<IntWritable, MovieRatingWritable, IntWritable, MovieRatingWritable> {
    
    private MovieRatingWritable result = new MovieRatingWritable();
    
    @Override
    public void reduce(IntWritable key, Iterable<MovieRatingWritable> values, Context context)
            throws IOException, InterruptedException {
        
        // 初始化聚合结果
        double totalRating = 0.0;
        int ratingCount = 0;
        double maxRating = 0.0;
        double minRating = 5.0;  // 初始化为最大可能值
        
        // 遍历所有评分信息进行聚合
        for (MovieRatingWritable value : values) {
            totalRating += value.getTotalRating();
            ratingCount += value.getRatingCount();
            maxRating = Math.max(maxRating, value.getMaxRating());
            minRating = Math.min(minRating, value.getMinRating());
        }
        
        // 设置聚合结果
        result.setTotalRating(totalRating);
        result.setRatingCount(ratingCount);
        result.setMaxRating(maxRating);
        result.setMinRating(minRating);
        
        // 输出聚合结果
        context.write(key, result);
        
        // 统计Combiner处理的记录数
        context.getCounter("COMBINER_STATS", "COMBINED_RECORDS").increment(1);
        context.getCounter("COMBINER_STATS", "INPUT_RECORDS").increment(ratingCount);
    }
    
    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        super.setup(context);
        System.out.println("MovieStatsCombiner 初始化完成");
    }
    
    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        super.cleanup(context);
        System.out.println("MovieStatsCombiner 清理完成");
    }
}
