@echo off
REM 手动编译Java文件的脚本

echo === 手动编译MapReduce项目 ===
echo.

REM 设置变量
set SRC_DIR=src\main\java
set BUILD_DIR=target\classes
set LIB_DIR=lib

REM 尝试自动检测Hadoop路径
if defined HADOOP_HOME (
    echo 使用环境变量HADOOP_HOME: %HADOOP_HOME%
    set HADOOP_CLASSPATH=%HADOOP_HOME%\share\hadoop\common\*;%HADOOP_HOME%\share\hadoop\common\lib\*;%HADOOP_HOME%\share\hadoop\hdfs\*;%HADOOP_HOME%\share\hadoop\hdfs\lib\*;%HADOOP_HOME%\share\hadoop\mapreduce\*;%HADOOP_HOME%\share\hadoop\mapreduce\lib\*;%HADOOP_HOME%\share\hadoop\yarn\*;%HADOOP_HOME%\share\hadoop\yarn\lib\*
) else (
    echo 警告: HADOOP_HOME环境变量未设置，尝试使用hadoop classpath命令
    for /f "delims=" %%i in ('hadoop classpath 2^>nul') do set HADOOP_CLASSPATH=%%i
    if not defined HADOOP_CLASSPATH (
        echo 错误: 无法获取Hadoop classpath，请设置HADOOP_HOME环境变量
        echo 或确保hadoop命令在PATH中
        pause
        exit /b 1
    )
)

REM 创建目录
echo 1. 创建编译目录...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
echo ✓ 编译目录已创建: %BUILD_DIR%
echo.

REM 编译Java文件
echo 2. 编译Java源文件...
echo 编译路径: %SRC_DIR%\com\hadoop\movieanalysis\*.java
echo 输出目录: %BUILD_DIR%
echo.

javac -cp "%HADOOP_CLASSPATH%" -d "%BUILD_DIR%" "%SRC_DIR%\com\hadoop\movieanalysis\*.java"

if errorlevel 1 (
    echo ✗ 编译失败
    pause
    exit /b 1
)

echo ✓ 编译成功
echo.

REM 检查编译结果
echo 3. 检查编译结果...
if exist "%BUILD_DIR%\com\hadoop\movieanalysis\*.class" (
    echo ✓ 找到编译后的class文件:
    dir "%BUILD_DIR%\com\hadoop\movieanalysis\*.class" /b
) else (
    echo ✗ 未找到编译后的class文件
    pause
    exit /b 1
)
echo.

REM 创建JAR文件
echo 4. 创建JAR文件...
set JAR_NAME=movie-rating-analysis.jar
if not exist "target" mkdir "target"

cd "%BUILD_DIR%"
jar cf "..\..\target\%JAR_NAME%" com\hadoop\movieanalysis\*.class
cd ..\..

if exist "target\%JAR_NAME%" (
    echo ✓ JAR文件创建成功: target\%JAR_NAME%
    echo 文件大小: 
    dir "target\%JAR_NAME%" | findstr "%JAR_NAME%"
) else (
    echo ✗ JAR文件创建失败
    pause
    exit /b 1
)
echo.

echo === 编译完成 ===
echo JAR文件位置: target\%JAR_NAME%
echo.
echo 运行命令:
echo hadoop jar target\%JAR_NAME% com.hadoop.movieanalysis.MovieRatingAnalysis ^<input^> ^<output^>
echo.
pause
