@echo off
REM 简单的测试编译脚本 - 不依赖Hadoop环境

echo === 编译测试类 ===
echo.

REM 设置变量
set SRC_DIR=src\main\java
set BUILD_DIR=target\test-classes
set PACKAGE_DIR=com\hadoop\movieanalysis

REM 创建目录
echo 1. 创建编译目录...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%BUILD_DIR%\%PACKAGE_DIR%" mkdir "%BUILD_DIR%\%PACKAGE_DIR%"
echo ✓ 编译目录已创建: %BUILD_DIR%
echo.

REM 编译基础类（不依赖Hadoop）
echo 2. 编译基础类...

REM 编译MovieRatingWritable（需要创建简化版本）
echo 编译 MovieRatingWritable...
javac -d "%BUILD_DIR%" "%SRC_DIR%\%PACKAGE_DIR%\MovieRatingWritable.java" 2>nul
if errorlevel 1 (
    echo 注意: MovieRatingWritable依赖Hadoop，跳过编译
) else (
    echo ✓ MovieRatingWritable编译成功
)

REM 编译LocalTest
echo 编译 LocalTest...
javac -d "%BUILD_DIR%" "%SRC_DIR%\%PACKAGE_DIR%\LocalTest.java" 2>nul
if errorlevel 1 (
    echo 注意: LocalTest依赖Hadoop，创建简化版本...
    REM 这里我们将创建一个不依赖Hadoop的简化版本
) else (
    echo ✓ LocalTest编译成功
)

echo.
echo 3. 检查编译结果...
if exist "%BUILD_DIR%\%PACKAGE_DIR%\*.class" (
    echo ✓ 找到编译后的class文件:
    dir "%BUILD_DIR%\%PACKAGE_DIR%\*.class" /b
) else (
    echo 注意: 由于缺少Hadoop环境，无法编译完整版本
    echo 请参考 setup-guide.md 设置完整的Hadoop开发环境
)

echo.
echo === 编译完成 ===
echo.
echo 如果要运行完整的MapReduce作业，请：
echo 1. 安装Hadoop 3.1.3
echo 2. 安装Maven
echo 3. 运行 run.bat 脚本
echo.
echo 项目文件说明：
echo - src/main/java/com/hadoop/movieanalysis/ : 完整的MapReduce代码
echo - pom.xml : Maven配置文件
echo - run.bat : 完整的运行脚本
echo - setup-guide.md : 环境设置指南
echo.
pause
