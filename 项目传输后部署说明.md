# MapReduce电影评分分析 - 项目传输后部署说明

## 📋 适用场景
本说明适用于已将项目文件传输到CentOS系统的情况，无需逐个创建文件。

## 🎯 前提条件
- ✅ 项目文件已传输到CentOS系统的 `/root/ldl/` 目录
- ✅ CentOS系统已安装Java 8+
- ✅ CentOS系统已安装Hadoop 3.1.3
- ✅ 具有root权限

## 🚀 快速部署方式

### 方式一：使用一键部署脚本（推荐）
```bash
# 1. 确保项目文件在 /root/ldl/ 目录
ls -la /root/ldl/

# 2. 运行一键部署脚本
chmod +x 一键部署脚本.sh
./一键部署脚本.sh

# 3. 运行快速脚本
cd /root/movie-analysis-deploy
./quick_run.sh
```

### 方式二：按照详细教程手动部署
```bash
# 按照 "完整非本地部署教程.md" 逐步执行
```

## 📁 项目文件结构说明

### 传输前的项目结构（Windows/开发环境）
```
ldl/
├── src/main/java/com/hadoop/movieanalysis/
│   ├── MovieRatingAnalysis.java
│   ├── MovieStatsMapper.java
│   ├── MovieStatsReducer.java
│   ├── MovieStatsCombiner.java
│   ├── MoviePartitioner.java
│   └── MovieRatingWritable.java
├── ratings.dat
├── movies.dat
├── users.dat
├── pom.xml
└── README.md
```

### 传输后的CentOS目录结构
```
/root/ldl/                           # 传输后的原项目目录
├── src/main/java/com/hadoop/movieanalysis/
├── *.dat                            # 数据文件
└── 其他文件...

/root/movie-analysis-deploy/         # 部署目录（脚本自动创建）
├── src/com/hadoop/movieanalysis/    # 重新组织的源代码
├── classes/                         # 编译输出
├── data/                           # 数据文件
├── results/                        # 结果文件
├── movie-analysis.jar              # 编译后的JAR文件
└── quick_run.sh                    # 快速运行脚本
```

## 🔧 环境配置要求

### 1. Java环境
```bash
# 检查Java版本
java -version
javac -version

# 如果未安装Java，执行：
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel
```

### 2. Hadoop环境
```bash
# 检查Hadoop安装
ls -la /opt/hadoop
# 或者
ls -la /usr/local/hadoop

# 设置环境变量（根据实际路径调整）
export HADOOP_HOME=/opt/hadoop
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
```

### 3. 网络和端口
确保以下端口可用：
- 9870 (NameNode Web UI)
- 8088 (ResourceManager Web UI)
- 9000 (HDFS)

## 📝 详细部署步骤

### 第一步：检查传输文件
```bash
# 检查项目目录
ls -la /root/ldl/

# 检查Java源代码
ls -la /root/ldl/src/main/java/com/hadoop/movieanalysis/

# 检查数据文件
ls -la /root/ldl/*.dat
```

### 第二步：运行部署脚本
```bash
# 运行一键部署脚本
chmod +x 一键部署脚本.sh
./一键部署脚本.sh
```

### 第三步：验证部署结果
```bash
# 检查部署目录
ls -la /root/movie-analysis-deploy/

# 检查JAR文件
ls -la /root/movie-analysis-deploy/movie-analysis.jar

# 检查快速运行脚本
ls -la /root/movie-analysis-deploy/quick_run.sh
```

### 第四步：运行MapReduce作业
```bash
# 进入部署目录
cd /root/movie-analysis-deploy

# 运行快速脚本
./quick_run.sh
```

## 🔍 故障排除

### 问题1：项目目录不存在
```bash
# 检查实际的项目目录位置
find /root -name "*.dat" -type f
find /root -name "MovieRatingAnalysis.java" -type f

# 修改脚本中的 SOURCE_PROJECT_DIR 变量
```

### 问题2：Hadoop路径错误
```bash
# 查找Hadoop安装位置
find / -name "hadoop" -type d 2>/dev/null | grep -E "(opt|usr|root)"

# 修改脚本中的 HADOOP_HOME 变量
```

### 问题3：权限问题
```bash
# 设置执行权限
chmod +x 一键部署脚本.sh
chmod +x /root/movie-analysis-deploy/quick_run.sh

# 检查文件所有者
chown -R root:root /root/movie-analysis-deploy/
```

### 问题4：编译错误
```bash
# 检查Java版本兼容性
java -version

# 检查Hadoop类路径
echo $HADOOP_CLASSPATH

# 手动设置类路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*
```

## 📊 预期运行结果

### 成功部署后的输出
```
========================================
  部署完成！
========================================

部署目录: /root/movie-analysis-deploy

项目文件检查：
✓ Java源代码: 6 个文件
✓ 数据文件: 3 个文件
✓ JAR文件: 存在
✓ 快速运行脚本: 存在
```

### 成功运行后的输出示例
```
=== 电影评分分析结果（前10行）===
1    平均评分:4.15    评分次数:452    最高评分:5.0    最低评分:1.0    总评分:1875.0
2    平均评分:3.20    评分次数:131    最高评分:5.0    最低评分:1.0    总评分:419.0
3    平均评分:3.88    评分次数:90     最高评分:5.0    最低评分:1.0    总评分:349.0
```

## 🎯 关键文件说明

### 核心脚本文件
- **一键部署脚本.sh**: 自动化部署脚本，处理文件复制、编译、JAR创建
- **完整非本地部署教程.md**: 详细的手动部署教程
- **quick_run.sh**: 快速运行脚本（部署后自动生成）

### 核心Java文件
- **MovieRatingAnalysis.java**: 主程序类
- **MovieStatsMapper.java**: Mapper实现
- **MovieStatsReducer.java**: Reducer实现
- **MovieStatsCombiner.java**: Combiner优化
- **MoviePartitioner.java**: Partitioner优化
- **MovieRatingWritable.java**: 自定义数据类型

### 数据文件
- **ratings.dat**: 评分数据（约100万条记录）
- **movies.dat**: 电影数据（约3900部电影）
- **users.dat**: 用户数据（约6000个用户）

## ✅ 验证清单

部署完成后，请确认以下项目：

- [ ] 项目文件已从 `/root/ldl/` 复制到 `/root/movie-analysis-deploy/`
- [ ] Java源代码编译成功，生成6个class文件
- [ ] JAR文件创建成功：`movie-analysis.jar`
- [ ] 数据文件复制成功：3个.dat文件
- [ ] 快速运行脚本创建成功：`quick_run.sh`
- [ ] Hadoop服务启动成功
- [ ] MapReduce作业运行成功
- [ ] 结果文件生成成功

## 🎉 总结

通过本部署说明，您可以快速将已传输的项目文件部署到CentOS系统并运行MapReduce作业。整个过程包括：

1. **文件检查和复制** - 从传输目录复制到部署目录
2. **环境配置** - 自动检测和设置Java、Hadoop环境
3. **项目编译** - 编译Java源代码并创建JAR文件
4. **快速运行** - 一键启动Hadoop并运行MapReduce作业

**🚀 现在您可以开始部署了！**
