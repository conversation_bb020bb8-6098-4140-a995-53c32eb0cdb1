@echo off
REM 电影评分分析 MapReduce 作业运行脚本 (Windows版本)
REM 作者：MapReduce高阶编程作业
REM 功能：编译、打包、上传数据、运行MapReduce作业

echo === 电影评分分析 MapReduce 作业 ===
echo 开始时间: %date% %time%
echo.

REM 设置变量
set PROJECT_NAME=movie-rating-analysis
set JAR_NAME=%PROJECT_NAME%-1.0-SNAPSHOT-jar-with-dependencies.jar
set MAIN_CLASS=com.hadoop.movieanalysis.MovieRatingAnalysis
set HDFS_INPUT_DIR=/user/%USERNAME%/movie-analysis/input
set HDFS_OUTPUT_DIR=/user/%USERNAME%/movie-analysis/output
set LOCAL_DATA_DIR=.

REM 检查Hadoop是否运行
echo 1. 检查Hadoop服务状态...
jps | findstr "NameNode DataNode" >nul
if errorlevel 1 (
    echo 错误: Hadoop服务未运行，请先启动Hadoop集群
    echo 请运行: start-dfs.cmd 和 start-yarn.cmd
    pause
    exit /b 1
)
echo ✓ Hadoop服务正在运行
echo.

REM 编译项目
echo 2. 编译Maven项目...
call mvn clean compile
if errorlevel 1 (
    echo ✗ 编译失败
    pause
    exit /b 1
)
echo ✓ 编译成功
echo.

REM 打包项目
echo 3. 打包项目...
call mvn package
if errorlevel 1 (
    echo ✗ 打包失败
    pause
    exit /b 1
)
echo ✓ 打包成功
echo.

REM 检查JAR文件是否存在
if not exist "target\%JAR_NAME%" (
    echo ✗ JAR文件不存在: target\%JAR_NAME%
    pause
    exit /b 1
)
echo ✓ JAR文件已生成: target\%JAR_NAME%
echo.

REM 创建HDFS目录
echo 4. 准备HDFS目录...
hdfs dfs -rm -r %HDFS_OUTPUT_DIR% 2>nul
hdfs dfs -mkdir -p %HDFS_INPUT_DIR%
echo ✓ HDFS目录准备完成
echo.

REM 上传数据文件到HDFS
echo 5. 上传数据文件到HDFS...
if exist "%LOCAL_DATA_DIR%\ratings.dat" (
    hdfs dfs -put -f %LOCAL_DATA_DIR%\ratings.dat %HDFS_INPUT_DIR%/
    echo ✓ ratings.dat 上传成功
) else (
    echo ✗ 找不到 ratings.dat 文件
    pause
    exit /b 1
)

REM 可选：上传其他数据文件
if exist "%LOCAL_DATA_DIR%\movies.dat" (
    hdfs dfs -put -f %LOCAL_DATA_DIR%\movies.dat %HDFS_INPUT_DIR%/
    echo ✓ movies.dat 上传成功
)

if exist "%LOCAL_DATA_DIR%\users.dat" (
    hdfs dfs -put -f %LOCAL_DATA_DIR%\users.dat %HDFS_INPUT_DIR%/
    echo ✓ users.dat 上传成功
)
echo.

REM 显示HDFS输入目录内容
echo 6. 检查HDFS输入目录...
hdfs dfs -ls %HDFS_INPUT_DIR%
echo.

REM 运行MapReduce作业
echo 7. 运行MapReduce作业...
echo 输入路径: %HDFS_INPUT_DIR%/ratings.dat
echo 输出路径: %HDFS_OUTPUT_DIR%
echo.

hadoop jar target\%JAR_NAME% %MAIN_CLASS% %HDFS_INPUT_DIR%/ratings.dat %HDFS_OUTPUT_DIR%
if errorlevel 1 (
    echo ✗ MapReduce作业执行失败
    pause
    exit /b 1
)
echo ✓ MapReduce作业执行成功
echo.

REM 显示结果
echo 8. 显示分析结果...
echo --- 输出目录内容 ---
hdfs dfs -ls %HDFS_OUTPUT_DIR%
echo.

echo --- 分析结果（前20行）---
hdfs dfs -cat %HDFS_OUTPUT_DIR%/part-r-00000 | more
echo.

REM 可选：下载结果到本地
echo 9. 下载结果到本地...
if exist "output_local" rmdir /s /q output_local
hdfs dfs -get %HDFS_OUTPUT_DIR% output_local
echo ✓ 结果已下载到 output_local 目录
echo.

echo === 作业完成 ===
echo 结束时间: %date% %time%
echo 结果文件位置:
echo   HDFS: %HDFS_OUTPUT_DIR%
echo   本地: output_local\
echo.
echo 查看完整结果命令:
echo   hdfs dfs -cat %HDFS_OUTPUT_DIR%/part-r-*
echo   或者查看本地文件: type output_local\part-r-*
echo.
pause
