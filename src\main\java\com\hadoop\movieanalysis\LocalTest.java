package com.hadoop.movieanalysis;

import java.io.*;
import java.util.*;

/**
 * 本地测试类 - 在没有Hadoop环境的情况下测试MapReduce逻辑
 * 模拟MapReduce的执行过程，验证代码正确性
 */
public class LocalTest {
    
    public static void main(String[] args) {
        System.out.println("=== MapReduce本地测试 ===");
        System.out.println("模拟电影评分分析过程...\n");
        
        try {
            // 测试数据
            String[] testData = {
                "1::1::5::978300760",
                "2::1::4::978302109", 
                "3::1::3::978301968",
                "1::2::4::978300275",
                "2::2::5::978824291",
                "4::2::3::978302268",
                "1::3::5::978302039",
                "3::3::4::978300719",
                "5::3::2::978302268"
            };
            
            // 模拟Map阶段
            System.out.println("1. Map阶段处理:");
            Map<Integer, List<MovieRatingWritable>> mapOutput = simulateMapPhase(testData);
            
            // 模拟Combiner阶段
            System.out.println("\n2. Combiner阶段处理:");
            Map<Integer, MovieRatingWritable> combinerOutput = simulateCombinerPhase(mapOutput);
            
            // 模拟Reduce阶段
            System.out.println("\n3. Reduce阶段处理:");
            simulateReducePhase(combinerOutput);
            
            // 测试自定义数据类型
            System.out.println("\n4. 测试自定义数据类型:");
            testMovieRatingWritable();
            
            // 测试Partitioner
            System.out.println("\n5. 测试Partitioner:");
            testPartitioner();
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 模拟Map阶段
     */
    private static Map<Integer, List<MovieRatingWritable>> simulateMapPhase(String[] testData) {
        Map<Integer, List<MovieRatingWritable>> mapOutput = new HashMap<>();
        
        for (String line : testData) {
            try {
                // 解析输入行：UserID::MovieID::Rating::Timestamp
                String[] fields = line.split("::");
                if (fields.length != 4) continue;
                
                int movieId = Integer.parseInt(fields[1]);
                double rating = Double.parseDouble(fields[2]);
                
                // 创建MovieRatingWritable对象
                MovieRatingWritable ratingInfo = new MovieRatingWritable(rating);
                
                // 添加到map输出
                mapOutput.computeIfAbsent(movieId, k -> new ArrayList<>()).add(ratingInfo);
                
                System.out.printf("  Map输出: 电影ID=%d, 评分=%.1f%n", movieId, rating);
                
            } catch (Exception e) {
                System.err.println("处理行时出错: " + line);
            }
        }
        
        return mapOutput;
    }
    
    /**
     * 模拟Combiner阶段
     */
    private static Map<Integer, MovieRatingWritable> simulateCombinerPhase(
            Map<Integer, List<MovieRatingWritable>> mapOutput) {
        
        Map<Integer, MovieRatingWritable> combinerOutput = new HashMap<>();
        
        for (Map.Entry<Integer, List<MovieRatingWritable>> entry : mapOutput.entrySet()) {
            int movieId = entry.getKey();
            List<MovieRatingWritable> ratings = entry.getValue();
            
            // 聚合评分信息
            double totalRating = 0.0;
            int ratingCount = 0;
            double maxRating = 0.0;
            double minRating = 5.0;
            
            for (MovieRatingWritable rating : ratings) {
                totalRating += rating.getTotalRating();
                ratingCount += rating.getRatingCount();
                maxRating = Math.max(maxRating, rating.getMaxRating());
                minRating = Math.min(minRating, rating.getMinRating());
            }
            
            MovieRatingWritable result = new MovieRatingWritable(totalRating, ratingCount, maxRating, minRating);
            combinerOutput.put(movieId, result);
            
            System.out.printf("  Combiner输出: 电影ID=%d, %s%n", movieId, result.toString());
        }
        
        return combinerOutput;
    }
    
    /**
     * 模拟Reduce阶段
     */
    private static void simulateReducePhase(Map<Integer, MovieRatingWritable> combinerOutput) {
        
        System.out.println("  最终结果:");
        System.out.println("  电影ID\t平均评分\t评分次数\t最高评分\t最低评分");
        System.out.println("  ------------------------------------------------");
        
        for (Map.Entry<Integer, MovieRatingWritable> entry : combinerOutput.entrySet()) {
            int movieId = entry.getKey();
            MovieRatingWritable stats = entry.getValue();
            
            System.out.printf("  %d\t\t%.2f\t\t%d\t\t%.1f\t\t%.1f%n",
                movieId,
                stats.getAverageRating(),
                stats.getRatingCount(),
                stats.getMaxRating(),
                stats.getMinRating());
        }
    }
    
    /**
     * 测试MovieRatingWritable类
     */
    private static void testMovieRatingWritable() {
        // 创建测试对象
        MovieRatingWritable rating1 = new MovieRatingWritable(4.5);
        MovieRatingWritable rating2 = new MovieRatingWritable(3.0);
        
        System.out.println("  创建评分对象1: " + rating1);
        System.out.println("  创建评分对象2: " + rating2);
        
        // 测试合并功能
        rating1.merge(rating2);
        System.out.println("  合并后结果: " + rating1);
        
        // 测试序列化（模拟）
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            DataOutputStream dos = new DataOutputStream(baos);
            rating1.write(dos);
            
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            DataInputStream dis = new DataInputStream(bais);
            MovieRatingWritable rating3 = new MovieRatingWritable();
            rating3.readFields(dis);
            
            System.out.println("  序列化测试成功: " + rating3);
            
        } catch (IOException e) {
            System.err.println("  序列化测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Partitioner
     */
    private static void testPartitioner() {
        MoviePartitioner partitioner = new MoviePartitioner();
        
        // 测试不同电影ID的分区
        int[] movieIds = {1, 100, 500, 1000, 2000, 3000, 3999};
        int numPartitions = 3;
        
        System.out.printf("  测试Partitioner (分区数=%d):%n", numPartitions);
        for (int movieId : movieIds) {
            org.apache.hadoop.io.IntWritable key = new org.apache.hadoop.io.IntWritable(movieId);
            MovieRatingWritable value = new MovieRatingWritable(4.0);
            
            // 注意：这里会因为缺少Hadoop环境而出错，但展示了测试思路
            try {
                int partition = partitioner.getPartition(key, value, numPartitions);
                System.out.printf("    电影ID=%d -> 分区=%d%n", movieId, partition);
            } catch (Exception e) {
                // 模拟分区逻辑
                int maxMovieId = 4000;
                int partitionSize = maxMovieId / numPartitions;
                int partition = (movieId - 1) / partitionSize;
                if (partition >= numPartitions) partition = numPartitions - 1;
                System.out.printf("    电影ID=%d -> 分区=%d (模拟)%n", movieId, partition);
            }
        }
    }
}
