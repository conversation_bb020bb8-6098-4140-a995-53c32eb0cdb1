package com.hadoop.movieanalysis;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.hadoop.util.GenericOptionsParser;

/**
 * 电影评分分析主类
 * MapReduce高阶编程 - 电影网站用户影评分析
 * 
 * 功能：
 * 1. 分析每部电影的评分统计（平均分、最高分、最低分、评分次数）
 * 2. 使用Combiner优化性能
 * 3. 使用自定义Partitioner提高并行度
 * 4. 使用自定义数据类型优化数据传输
 */
public class MovieRatingAnalysis {
    
    public static void main(String[] args) throws Exception {
        
        // 创建配置对象
        Configuration conf = new Configuration();
        
        // 解析命令行参数
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        
        // 检查参数数量
        if (otherArgs.length != 2) {
            System.err.println("使用方法: MovieRatingAnalysis <输入路径> <输出路径>");
            System.err.println("示例: hadoop jar movie-analysis.jar com.hadoop.movieanalysis.MovieRatingAnalysis /input/ratings.dat /output");
            System.exit(2);
        }
        
        // 设置Hadoop配置参数
        conf.set("mapreduce.job.jar", "movie-rating-analysis-1.0-SNAPSHOT-jar-with-dependencies.jar");
        
        // 创建Job对象
        Job job = Job.getInstance(conf, "电影评分统计分析");
        
        // 设置主类
        job.setJarByClass(MovieRatingAnalysis.class);
        
        // 设置Mapper类
        job.setMapperClass(MovieStatsMapper.class);
        
        // 设置Combiner类（使用Combiner优化性能）
        job.setCombinerClass(MovieStatsCombiner.class);
        
        // 设置Reducer类
        job.setReducerClass(MovieStatsReducer.class);
        
        // 设置自定义Partitioner
        job.setPartitionerClass(MoviePartitioner.class);
        
        // 设置Reducer数量（可以根据集群大小调整）
        job.setNumReduceTasks(3);
        
        // 设置输出key和value的类型
        job.setOutputKeyClass(IntWritable.class);
        job.setOutputValueClass(Text.class);
        
        // 设置Map输出key和value的类型
        job.setMapOutputKeyClass(IntWritable.class);
        job.setMapOutputValueClass(MovieRatingWritable.class);
        
        // 设置输入和输出格式
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);
        
        // 设置输入和输出路径
        FileInputFormat.addInputPath(job, new Path(otherArgs[0]));
        FileOutputFormat.setOutputPath(job, new Path(otherArgs[1]));
        
        // 设置作业描述
        job.setJobName("电影评分统计分析 - MapReduce高阶编程");
        
        // 打印作业信息
        System.out.println("=== 电影评分分析作业配置 ===");
        System.out.println("输入路径: " + otherArgs[0]);
        System.out.println("输出路径: " + otherArgs[1]);
        System.out.println("Mapper类: " + MovieStatsMapper.class.getSimpleName());
        System.out.println("Combiner类: " + MovieStatsCombiner.class.getSimpleName());
        System.out.println("Reducer类: " + MovieStatsReducer.class.getSimpleName());
        System.out.println("Partitioner类: " + MoviePartitioner.class.getSimpleName());
        System.out.println("Reducer数量: " + job.getNumReduceTasks());
        System.out.println("========================");
        
        // 等待作业完成
        boolean success = job.waitForCompletion(true);
        
        // 打印作业统计信息
        if (success) {
            System.out.println("\n=== 作业执行统计 ===");
            System.out.println("处理的记录数: " + 
                job.getCounters().findCounter("MAPPER_STATS", "PROCESSED_RECORDS").getValue());
            System.out.println("处理的电影数: " + 
                job.getCounters().findCounter("REDUCER_STATS", "PROCESSED_MOVIES").getValue());
            System.out.println("总评分数: " + 
                job.getCounters().findCounter("REDUCER_STATS", "TOTAL_RATINGS").getValue());
            System.out.println("高评分电影数(>=4.0): " + 
                job.getCounters().findCounter("MOVIE_QUALITY", "HIGH_RATED_MOVIES").getValue());
            System.out.println("中等评分电影数(3.0-4.0): " + 
                job.getCounters().findCounter("MOVIE_QUALITY", "MEDIUM_RATED_MOVIES").getValue());
            System.out.println("低评分电影数(<3.0): " + 
                job.getCounters().findCounter("MOVIE_QUALITY", "LOW_RATED_MOVIES").getValue());
            System.out.println("热门电影数(评分>=100次): " + 
                job.getCounters().findCounter("MOVIE_POPULARITY", "POPULAR_MOVIES").getValue());
            System.out.println("==================");
        }
        
        // 退出程序
        System.exit(success ? 0 : 1);
    }
}
