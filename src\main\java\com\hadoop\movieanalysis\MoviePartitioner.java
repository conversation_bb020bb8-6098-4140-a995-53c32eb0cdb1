package com.hadoop.movieanalysis;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.mapreduce.Partitioner;

/**
 * 自定义Partitioner：根据电影ID范围将数据分配到不同的Reducer
 * 这样可以确保数据的均匀分布，提高并行处理效率
 */
public class MoviePartitioner extends Partitioner<IntWritable, MovieRatingWritable> {
    
    @Override
    public int getPartition(IntWritable key, MovieRatingWritable value, int numPartitions) {
        
        // 如果只有一个分区，直接返回0
        if (numPartitions == 1) {
            return 0;
        }
        
        int movieId = key.get();
        
        // 根据电影ID范围进行分区
        // 假设电影ID范围是1-4000（根据实际数据调整）
        int maxMovieId = 4000;
        int partitionSize = maxMovieId / numPartitions;
        
        // 计算分区号
        int partition = (movieId - 1) / partitionSize;
        
        // 确保分区号在有效范围内
        if (partition >= numPartitions) {
            partition = numPartitions - 1;
        }
        
        return partition;
    }
}
