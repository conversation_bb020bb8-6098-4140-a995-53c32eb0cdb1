package com.hadoop.movieanalysis;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

import org.apache.hadoop.io.WritableComparable;

/**
 * 自定义数据类型，用于存储电影评分统计信息
 * 包含：总评分、评分次数、最高评分、最低评分
 */
public class MovieRatingWritable implements WritableComparable<MovieRatingWritable> {
    
    private double totalRating;    // 总评分
    private int ratingCount;       // 评分次数
    private double maxRating;      // 最高评分
    private double minRating;      // 最低评分
    
    // 默认构造函数
    public MovieRatingWritable() {
        this.totalRating = 0.0;
        this.ratingCount = 0;
        this.maxRating = 0.0;
        this.minRating = 5.0;  // 初始化为最大可能值
    }
    
    // 带参数构造函数
    public MovieRatingWritable(double rating) {
        this.totalRating = rating;
        this.ratingCount = 1;
        this.maxRating = rating;
        this.minRating = rating;
    }
    
    // 带所有参数的构造函数
    public MovieRatingWritable(double totalRating, int ratingCount, 
                              double maxRating, double minRating) {
        this.totalRating = totalRating;
        this.ratingCount = ratingCount;
        this.maxRating = maxRating;
        this.minRating = minRating;
    }
    
    /**
     * 合并两个MovieRatingWritable对象
     */
    public void merge(MovieRatingWritable other) {
        this.totalRating += other.totalRating;
        this.ratingCount += other.ratingCount;
        this.maxRating = Math.max(this.maxRating, other.maxRating);
        this.minRating = Math.min(this.minRating, other.minRating);
    }
    
    /**
     * 计算平均评分
     */
    public double getAverageRating() {
        return ratingCount > 0 ? totalRating / ratingCount : 0.0;
    }
    
    // Getter和Setter方法
    public double getTotalRating() {
        return totalRating;
    }
    
    public void setTotalRating(double totalRating) {
        this.totalRating = totalRating;
    }
    
    public int getRatingCount() {
        return ratingCount;
    }
    
    public void setRatingCount(int ratingCount) {
        this.ratingCount = ratingCount;
    }
    
    public double getMaxRating() {
        return maxRating;
    }
    
    public void setMaxRating(double maxRating) {
        this.maxRating = maxRating;
    }
    
    public double getMinRating() {
        return minRating;
    }
    
    public void setMinRating(double minRating) {
        this.minRating = minRating;
    }
    
    @Override
    public void write(DataOutput out) throws IOException {
        out.writeDouble(totalRating);
        out.writeInt(ratingCount);
        out.writeDouble(maxRating);
        out.writeDouble(minRating);
    }
    
    @Override
    public void readFields(DataInput in) throws IOException {
        totalRating = in.readDouble();
        ratingCount = in.readInt();
        maxRating = in.readDouble();
        minRating = in.readDouble();
    }
    
    @Override
    public int compareTo(MovieRatingWritable other) {
        // 按平均评分降序排列
        double thisAvg = this.getAverageRating();
        double otherAvg = other.getAverageRating();
        return Double.compare(otherAvg, thisAvg);
    }
    
    @Override
    public String toString() {
        return String.format("平均评分:%.2f, 评分次数:%d, 最高评分:%.1f, 最低评分:%.1f",
                getAverageRating(), ratingCount, maxRating, minRating);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MovieRatingWritable that = (MovieRatingWritable) obj;
        return Double.compare(that.totalRating, totalRating) == 0 &&
               ratingCount == that.ratingCount &&
               Double.compare(that.maxRating, maxRating) == 0 &&
               Double.compare(that.minRating, minRating) == 0;
    }
    
    @Override
    public int hashCode() {
        int result;
        long temp;
        temp = Double.doubleToLongBits(totalRating);
        result = (int) (temp ^ (temp >>> 32));
        result = 31 * result + ratingCount;
        temp = Double.doubleToLongBits(maxRating);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(minRating);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        return result;
    }
}
