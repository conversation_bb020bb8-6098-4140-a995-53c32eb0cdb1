#!/bin/bash

# MapReduce项目编译修正脚本
# 解决javac找不到文件的问题

echo "=== MapReduce项目编译修正脚本 ==="
echo "开始时间: $(date)"
echo ""

# 设置变量
PROJECT_DIR="/root/movie-analysis-deploy"
HADOOP_HOME="/opt/hadoop"  # 根据实际路径调整

# 检查当前目录
echo "当前目录: $(pwd)"

# 进入项目目录
cd $PROJECT_DIR
echo "进入项目目录: $PROJECT_DIR"

# 检查目录结构
echo ""
echo "检查项目目录结构："
ls -la

echo ""
echo "检查Java源代码目录："
ls -la src/com/hadoop/movieanalysis/

# 设置环境变量
echo ""
echo "设置环境变量..."

# 自动检测Java路径
if [ -z "$JAVA_HOME" ]; then
    export JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
fi

# 检查Hadoop路径
if [ ! -d "$HADOOP_HOME" ]; then
    echo "检测Hadoop路径..."
    for path in /opt/hadoop /usr/local/hadoop /root/hadoop; do
        if [ -d "$path" ]; then
            export HADOOP_HOME="$path"
            echo "找到Hadoop路径: $HADOOP_HOME"
            break
        fi
    done
fi

# 设置Hadoop类路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

echo "JAVA_HOME: $JAVA_HOME"
echo "HADOOP_HOME: $HADOOP_HOME"

# 清理旧的编译文件
echo ""
echo "清理旧的编译文件..."
rm -rf classes/*
rm -f movie-analysis.jar
mkdir -p classes

# 检查Java源文件是否存在
echo ""
echo "检查Java源文件："
JAVA_FILES=(
    "MovieRatingWritable.java"
    "MovieStatsMapper.java"
    "MovieStatsCombiner.java"
    "MoviePartitioner.java"
    "MovieStatsReducer.java"
    "MovieRatingAnalysis.java"
)

MISSING_FILES=0
for file in "${JAVA_FILES[@]}"; do
    if [ -f "src/com/hadoop/movieanalysis/$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 缺失"
        MISSING_FILES=$((MISSING_FILES + 1))
    fi
done

if [ $MISSING_FILES -gt 0 ]; then
    echo "错误: 有 $MISSING_FILES 个Java文件缺失"
    exit 1
fi

# 编译Java文件（正确的方式）
echo ""
echo "编译Java源文件..."
echo "编译命令: javac -cp \$HADOOP_CLASSPATH -d classes src/com/hadoop/movieanalysis/*.java"

# 进入源代码目录进行编译
cd src/com/hadoop/movieanalysis
javac -cp $HADOOP_CLASSPATH -d $PROJECT_DIR/classes *.java

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "✓ Java文件编译成功"
    
    # 返回项目根目录
    cd $PROJECT_DIR
    
    # 显示编译结果
    echo ""
    echo "编译输出文件："
    ls -la classes/com/hadoop/movieanalysis/
    
    # 创建JAR文件
    echo ""
    echo "创建JAR文件..."
    cd classes
    jar cf ../movie-analysis.jar com/hadoop/movieanalysis/*.class
    
    # 返回项目根目录
    cd $PROJECT_DIR
    
    # 验证JAR文件
    if [ -f "movie-analysis.jar" ]; then
        echo "✓ JAR文件创建成功"
        echo "JAR文件信息："
        ls -la movie-analysis.jar
        
        echo ""
        echo "JAR文件内容："
        jar tf movie-analysis.jar
    else
        echo "✗ JAR文件创建失败"
        exit 1
    fi
    
else
    echo "✗ Java文件编译失败"
    echo ""
    echo "可能的原因："
    echo "1. HADOOP_CLASSPATH 设置不正确"
    echo "2. Java源代码有语法错误"
    echo "3. Hadoop JAR文件不存在"
    echo ""
    echo "请检查以下内容："
    echo "HADOOP_CLASSPATH=$HADOOP_CLASSPATH"
    echo ""
    echo "检查Hadoop JAR文件是否存在："
    ls -la $HADOOP_HOME/share/hadoop/common/hadoop-common-*.jar 2>/dev/null || echo "hadoop-common JAR文件不存在"
    ls -la $HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-core-*.jar 2>/dev/null || echo "mapreduce-client-core JAR文件不存在"
    
    exit 1
fi

echo ""
echo "=== 编译完成 ==="
echo "结束时间: $(date)"
echo ""
echo "下一步："
echo "1. 启动Hadoop服务: \$HADOOP_HOME/sbin/start-dfs.sh && \$HADOOP_HOME/sbin/start-yarn.sh"
echo "2. 运行MapReduce作业: ./quick_run.sh"
echo "3. 或者手动运行: hadoop jar movie-analysis.jar com.hadoop.movieanalysis.MovieRatingAnalysis /input /output"
