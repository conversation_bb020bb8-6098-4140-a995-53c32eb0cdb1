package com.hadoop.movieanalysis;

import java.io.IOException;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

/**
 * Reducer类：最终聚合每部电影的评分统计信息
 * 输入：Key=MovieID, Value=Iterable<MovieRatingWritable>
 * 输出：Key=MovieID, Value=Text(格式化的统计信息)
 */
public class MovieStatsReducer extends Reducer<IntWritable, MovieRatingWritable, IntWritable, Text> {
    
    private Text result = new Text();
    
    @Override
    public void reduce(IntWritable key, Iterable<MovieRatingWritable> values, Context context)
            throws IOException, InterruptedException {
        
        // 初始化聚合变量
        double totalRating = 0.0;
        int ratingCount = 0;
        double maxRating = 0.0;
        double minRating = 5.0;  // 初始化为最大可能值
        
        // 遍历所有评分信息进行最终聚合
        for (MovieRatingWritable value : values) {
            totalRating += value.getTotalRating();
            ratingCount += value.getRatingCount();
            maxRating = Math.max(maxRating, value.getMaxRating());
            minRating = Math.min(minRating, value.getMinRating());
        }
        
        // 计算平均评分
        double averageRating = ratingCount > 0 ? totalRating / ratingCount : 0.0;
        
        // 格式化输出结果
        String resultString = String.format(
            "平均评分:%.2f\t评分次数:%d\t最高评分:%.1f\t最低评分:%.1f\t总评分:%.1f",
            averageRating, ratingCount, maxRating, minRating, totalRating
        );
        
        result.set(resultString);
        
        // 输出最终结果
        context.write(key, result);
        
        // 统计Reducer处理的记录数
        context.getCounter("REDUCER_STATS", "PROCESSED_MOVIES").increment(1);
        context.getCounter("REDUCER_STATS", "TOTAL_RATINGS").increment(ratingCount);
        
        // 统计不同评分范围的电影数量
        if (averageRating >= 4.0) {
            context.getCounter("MOVIE_QUALITY", "HIGH_RATED_MOVIES").increment(1);
        } else if (averageRating >= 3.0) {
            context.getCounter("MOVIE_QUALITY", "MEDIUM_RATED_MOVIES").increment(1);
        } else {
            context.getCounter("MOVIE_QUALITY", "LOW_RATED_MOVIES").increment(1);
        }
        
        // 统计热门电影（评分次数超过100的电影）
        if (ratingCount >= 100) {
            context.getCounter("MOVIE_POPULARITY", "POPULAR_MOVIES").increment(1);
        }
    }
    
    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        super.setup(context);
        System.out.println("MovieStatsReducer 初始化完成");
    }
    
    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        super.cleanup(context);
        System.out.println("MovieStatsReducer 清理完成");
    }
}
